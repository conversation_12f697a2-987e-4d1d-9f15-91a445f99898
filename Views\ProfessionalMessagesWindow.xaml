<Window x:Class="SFDSystem.Views.ProfessionalMessagesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:SFDSystem.Views"
        Title="نظام إرسال الرسائل للسائقين"
        Height="850" Width="1300"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="White"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="15" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Button Style -->
        <Style x:Key="ProfessionalButton" TargetType="Button">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="10"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5A6268"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#495057"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button -->
        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#FFC107"/>
            <Setter Property="Foreground" Value="#212529"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0A800"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Padding="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#6C757D"/>
                                <Setter Property="Background" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8">
                            <Grid>
                                <ToggleButton x:Name="ToggleButton" 
                                            Background="Transparent"
                                            BorderThickness="0"
                                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                            ClickMode="Press"/>
                                <ContentPresenter x:Name="ContentSite"
                                                IsHitTestVisible="False"
                                                Content="{TemplateBinding SelectionBoxItem}"
                                                ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                Margin="{TemplateBinding Padding}"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Left"/>
                                <Popup x:Name="Popup"
                                     Placement="Bottom"
                                     IsOpen="{TemplateBinding IsDropDownOpen}"
                                     AllowsTransparency="True"
                                     Focusable="False"
                                     PopupAnimation="Slide">
                                    <Grid x:Name="DropDown"
                                        SnapsToDevicePixels="True"
                                        MinWidth="{TemplateBinding ActualWidth}"
                                        MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                        <Border x:Name="DropDownBorder"
                                              Background="White"
                                              BorderThickness="1"
                                              BorderBrush="#DEE2E6"
                                              CornerRadius="8"/>
                                        <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                        </ScrollViewer>
                                    </Grid>
                                </Popup>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Content -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Simple Header -->
        <Border Grid.Row="0" Background="White" Height="60" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Title with better text wrapping -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="30,0">
                    <TextBlock Text="نظام إرسال الرسائل للسائقين"
                              FontSize="18" FontWeight="Bold"
                              Foreground="#333333"
                              TextWrapping="Wrap"
                              MaxWidth="400"/>
                </StackPanel>

                <!-- Visit Number and Duration with better spacing -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <Border Background="#E3F2FD" CornerRadius="8" Padding="8,4" Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="رقم الزيارة:" FontSize="12" FontWeight="SemiBold"
                                      Foreground="#1976D2" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding VisitNumber, FallbackValue='15'}" FontSize="12" FontWeight="Bold"
                                      Foreground="#1976D2"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#E8F5E8" CornerRadius="8" Padding="8,4">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="المدة:" FontSize="12" FontWeight="SemiBold"
                                      Foreground="#388E3C" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding CorrectDuration, FallbackValue='3 أيام'}" FontSize="12" FontWeight="Bold"
                                      Foreground="#388E3C"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="1" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="850"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Driver Selection -->
            <Border Grid.Column="0" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Dynamic Header with improved text display -->
                    <Grid Grid.Row="0" Margin="0,0,0,20">
                        <StackPanel>
                            <!-- Main title with better wrapping -->
                            <Border Background="#F8F9FA" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                                <TextBlock Text="{Binding SelectedDriversDisplayText, FallbackValue='👥 يرجى اختيار سائق واحد على الأقل'}"
                                          FontSize="16" FontWeight="SemiBold"
                                          Foreground="#2C3E50"
                                          HorizontalAlignment="Center"
                                          TextWrapping="Wrap"
                                          TextAlignment="Center"
                                          LineHeight="24"
                                          MaxWidth="750"/>
                            </Border>

                            <!-- Clear Selection Button (visible only when drivers are selected) -->
                            <Button x:Name="ClearSelectionButton"
                                   Content="🗑️ مسح الاختيار"
                                   Height="30" MinWidth="120"
                                   Background="#DC3545" Foreground="White"
                                   BorderThickness="0" FontSize="12"
                                   Cursor="Hand" Margin="0,5,0,0"
                                   Click="ClearSelectionButton_Click"
                                   HorizontalAlignment="Center">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding SelectedDriversCount}" Value="0">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                            </DataTrigger>
                                            <DataTrigger>
                                                <DataTrigger.Binding>
                                                    <Binding Path="SelectedDriversCount">
                                                        <Binding.Converter>
                                                            <local:GreaterThanZeroConverter/>
                                                        </Binding.Converter>
                                                    </Binding>
                                                </DataTrigger.Binding>
                                                <DataTrigger.Value>True</DataTrigger.Value>
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="6"
                                               BorderThickness="{TemplateBinding BorderThickness}"
                                               BorderBrush="{TemplateBinding BorderBrush}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>
                        </StackPanel>
                    </Grid>



                    <!-- Professional Driver Management Section -->
                    <Border Background="White" CornerRadius="15" Padding="25" Margin="0,20,0,20" Grid.Row="1">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="320" ShadowDepth="3" Opacity="0.3"/>
                        </Border.Effect>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Statistics Cards with improved layout -->
                            <Grid Grid.Row="0" Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Total Drivers Card -->
                                <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="12" Padding="15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center" MinHeight="80">
                                        <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="{Binding AllDriversCount, FallbackValue='25'}" FontSize="20" FontWeight="Bold"
                                                  Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="إجمالي السائقين" FontSize="12" Foreground="#666"
                                                  HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Available Drivers Card -->
                                <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="12" Padding="15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center" MinHeight="80">
                                        <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="{Binding AvailableDriversCount, FallbackValue='18'}" FontSize="20" FontWeight="Bold"
                                                  Foreground="#388E3C" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="السائقين المتاحين" FontSize="12" Foreground="#666"
                                                  HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Selected Drivers Card -->
                                <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="12" Padding="15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center" MinHeight="80">
                                        <TextBlock Text="🎯" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="{Binding SelectedDriversCount, FallbackValue='3'}" FontSize="20" FontWeight="Bold"
                                                  Foreground="#F57C00" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="السائقين المحددين" FontSize="12" Foreground="#666"
                                                  HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Active Contracts Card -->
                            </Grid>

                            <!-- Main Driver Selection Button -->
                            <Button x:Name="OpenDriverManagementButton"
                                   Click="OpenDriverManagementButton_Click"
                                   Grid.Row="1"
                                   Height="70"
                                   Margin="0,20,0,0"
                                   Cursor="Hand"
                                   HorizontalAlignment="Stretch">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                    <GradientStop Color="#667eea" Offset="0"/>
                                                    <GradientStop Color="#764ba2" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                           CornerRadius="15"
                                                           BorderThickness="{TemplateBinding BorderThickness}"
                                                           BorderBrush="{TemplateBinding BorderBrush}">
                                                        <Border.Effect>
                                                            <DropShadowEffect Color="#667eea" Direction="270" ShadowDepth="3" Opacity="0.4" BlurRadius="10"/>
                                                        </Border.Effect>
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background">
                                                                <Setter.Value>
                                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                        <GradientStop Color="#5a67d8" Offset="0"/>
                                                                        <GradientStop Color="#6b46c1" Offset="1"/>
                                                                    </LinearGradientBrush>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="True">
                                                            <Setter Property="Background">
                                                                <Setter.Value>
                                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                        <GradientStop Color="#4c51bf" Offset="0"/>
                                                                        <GradientStop Color="#553c9a" Offset="1"/>
                                                                    </LinearGradientBrush>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Icon -->
                                    <Border Grid.Column="0" Background="#33FFFFFF" CornerRadius="25"
                                           Width="50" Height="50" Margin="20,0,10,0">
                                        <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>

                                    <!-- Content with better text handling -->
                                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10,0">
                                        <TextBlock Text="اختيار سائقين"
                                                  FontSize="20" FontWeight="Bold"
                                                  Foreground="White"
                                                  HorizontalAlignment="Center"
                                                  TextWrapping="Wrap"
                                                  TextAlignment="Center"/>
                                        <TextBlock Text="انقر لاختيار السائقين المطلوبين"
                                                  FontSize="12"
                                                  Foreground="#E0E0E0"
                                                  HorizontalAlignment="Center"
                                                  TextWrapping="Wrap"
                                                  TextAlignment="Center"
                                                  Margin="0,5,0,0"/>
                                    </StackPanel>

                                    <!-- Arrow -->
                                    <Border Grid.Column="2" Background="#33FFFFFF" CornerRadius="25"
                                           Width="45" Height="45" Margin="10,0,20,0">
                                        <TextBlock Text="▶" FontSize="18" HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                </Grid>
                            </Button>

                            <!-- Quick Actions Buttons -->
                            <Grid Grid.Row="2" Margin="0,20,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Select All Button -->
                                <Button Grid.Column="0" Height="40" Margin="5" Background="#28A745" Foreground="White"
                                       BorderThickness="0" FontSize="12" Cursor="Hand"
                                       ToolTip="تحديد جميع السائقين المتاحين"
                                       FontWeight="SemiBold">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                   CornerRadius="8"
                                                   BorderThickness="{TemplateBinding BorderThickness}"
                                                   BorderBrush="{TemplateBinding BorderBrush}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="✅" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="تحديد الكل" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>

                                <!-- Clear Selection Button -->
                                <Button Grid.Column="1" Height="40" Margin="5" Background="#6C757D" Foreground="White"
                                       BorderThickness="0" FontSize="12" Cursor="Hand"
                                       ToolTip="إلغاء تحديد جميع السائقين"
                                       FontWeight="SemiBold">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                   CornerRadius="8"
                                                   BorderThickness="{TemplateBinding BorderThickness}"
                                                   BorderBrush="{TemplateBinding BorderBrush}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="❌" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="إلغاء التحديد" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>

                                <!-- Refresh Button -->
                                <Button Grid.Column="2" Height="40" Margin="5" Background="#17A2B8" Foreground="White"
                                       BorderThickness="0" FontSize="12" Cursor="Hand"
                                       ToolTip="تحديث بيانات السائقين"
                                       FontWeight="SemiBold">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                   CornerRadius="8"
                                                   BorderThickness="{TemplateBinding BorderThickness}"
                                                   BorderBrush="{TemplateBinding BorderBrush}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="🔄" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="تحديث" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- Offers Results Section -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="10" Padding="15" Margin="0,20,0,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Header with better text wrapping -->
                            <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,15">
                                <TextBlock Text="🏆 السائقين الذين تم تقديم أسعارهم"
                                          FontSize="16" FontWeight="Bold" Foreground="#2C3E50"
                                          HorizontalAlignment="Center"
                                          TextWrapping="Wrap"
                                          TextAlignment="Center"
                                          MaxWidth="400"/>
                            </Border>

                            <DataGrid Grid.Row="1" x:Name="OffersResultsGrid"
                                     ItemsSource="{Binding OffersResults}"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     GridLinesVisibility="None"
                                     HeadersVisibility="Column"
                                     Background="White"
                                     BorderThickness="1"
                                     BorderBrush="#DEE2E6"
                                     AlternatingRowBackground="#F8F9FA"
                                     FontSize="12"
                                     RowHeight="Auto"
                                     MinRowHeight="45"
                                     MinHeight="200"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Auto">

                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#6C757D"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="Height" Value="35"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#DEE2E6"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>

                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Background" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="MinHeight" Value="45"/>
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#2196F3"/>
                                                <Setter Property="Foreground" Value="White"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>

                                <DataGrid.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="VerticalAlignment" Value="Top"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Padding" Value="8,8"/>
                                        <Setter Property="VerticalContentAlignment" Value="Top"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#2196F3"/>
                                                <Setter Property="Foreground" Value="White"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.CellStyle>

                                <DataGrid.Columns>
                                    <!-- اسم السائق مع تحسين العرض -->
                                    <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="180">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="TextWrapping" Value="Wrap"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="Foreground" Value="#2C3E50"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Padding" Value="5"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- السعر المقدم -->
                                    <DataGridTextColumn Header="السعر المقدم" Binding="{Binding OfferedPrice}" Width="140">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="Foreground" Value="#28A745"/>
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- عمود الحالة محسن -->
                                    <DataGridTemplateColumn Header="الحالة" Width="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4">
                                                    <TextBlock Text="{Binding Status}"
                                                              FontWeight="Bold"
                                                              FontSize="11"
                                                              Foreground="White"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Cursor="Hand"
                                                              MouseLeftButtonDown="ToggleWinnerStatus_Click"
                                                              Tag="{Binding}"
                                                              ToolTip="اضغط لتغيير الحالة"/>
                                                </Border>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <!-- تاريخ التقديم -->
                                    <DataGridTextColumn Header="تاريخ التقديم" Binding="{Binding SubmissionDate}" Width="140">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#6C757D"/>
                                                <Setter Property="FontSize" Value="12"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- نص الرسالة محسن -->
                                    <DataGridTextColumn Header="📱 نص الرسالة" Binding="{Binding MessageText}" Width="*" MinWidth="250">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="TextWrapping" Value="Wrap"/>
                                                <Setter Property="MaxWidth" Value="400"/>
                                                <Setter Property="ToolTip" Value="{Binding MessageText}"/>
                                                <Setter Property="Foreground" Value="#2196F3"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="LineHeight" Value="18"/>
                                                <Setter Property="Padding" Value="8,5"/>
                                                <Setter Property="VerticalAlignment" Value="Top"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>

                    <!-- Action Buttons -->
                    <Grid Grid.Row="3" Margin="0,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="6"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="6"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="6"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="OffersButton" Click="OffersButton_Click"
                               Background="#FF9800" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="8"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       BorderBrush="{TemplateBinding BorderBrush}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#E68900"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#CC7A00"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="Black" Direction="320" ShadowDepth="1" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🏆" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="عروض الأسعار" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <!-- Edit Offers Button - Professional Design -->
                        <Button Grid.Column="2" x:Name="EditOffersButton" Click="EditOffersButton_Click"
                               Background="#6C5CE7" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand"
                               ToolTip="تعديل عروض الأسعار المحفوظة للزيارة المحددة">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="10"
                                                       BorderThickness="2"
                                                       BorderBrush="#5A4FCF">
                                                    <Grid>
                                                        <Border x:Name="GlowBorder"
                                                               CornerRadius="10"
                                                               Opacity="0">
                                                            <Border.Background>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#FFFFFF" Offset="0" />
                                                                    <GradientStop Color="Transparent" Offset="1" />
                                                                </LinearGradientBrush>
                                                            </Border.Background>
                                                        </Border>
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"/>
                                                    </Grid>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#5A4FCF"/>
                                                        <Setter TargetName="GlowBorder" Property="Opacity" Value="0.2"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#4834D4"/>
                                                        <Setter TargetName="GlowBorder" Property="Opacity" Value="0.4"/>
                                                    </Trigger>
                                                    <Trigger Property="IsEnabled" Value="False">
                                                        <Setter Property="Background" Value="#CCCCCC"/>
                                                        <Setter Property="Foreground" Value="#666666"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="#6C5CE7" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <Border Background="#FFFFFF" CornerRadius="12" Width="24" Height="24" Margin="0,0,8,0">
                                    <TextBlock Text="✏️" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="تعديل العروض" FontSize="11" FontWeight="Bold"/>
                                    <TextBlock Text="تحرير البيانات المحفوظة" FontSize="8" Opacity="0.8"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="4" x:Name="SaveOffersButton" Click="SaveOffersButton_Click"
                               Background="#28A745" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="8"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       BorderBrush="{TemplateBinding BorderBrush}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#218838"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#1E7E34"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="Black" Direction="320" ShadowDepth="1" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💾" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="حفظ" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="6" x:Name="ClearOffersButton" Click="ClearOffersButton_Click"
                               Background="#DC3545" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="8"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       BorderBrush="{TemplateBinding BorderBrush}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#C82333"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#BD2130"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="Black" Direction="320" ShadowDepth="1" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🗑️" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="مسح النتائج" FontSize="12"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </Border>

            <!-- Right Panel - Message Content -->
            <Border Grid.Column="2" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>





                    <!-- Message Content -->
                    <Grid Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" FontSize="16" FontWeight="Bold"
                                  Foreground="#495057" Margin="0,0,0,10" HorizontalAlignment="Center"><Run Text="📄 محتوى الرسالة "/><Run FlowDirection="RightToLeft" Language="ar-ye" Text="للسائقين"/></TextBlock>

                        <!-- Message Templates -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <TextBlock Text="📋 قالب الرسالة:" FontSize="13" FontWeight="SemiBold"
                                      Foreground="#495057" VerticalAlignment="Center" Margin="0,0,15,0"/>

                            <ComboBox Height="35" Width="250" FontSize="13" Padding="10,8"
                                     ItemsSource="{Binding MessageTemplates}"
                                     SelectedItem="{Binding SelectedMessageTemplate}"
                                     DisplayMemberPath="Name"
                                     Background="White"
                                     Foreground="#333333"
                                     BorderBrush="#CCCCCC"
                                     BorderThickness="1"
                                     SelectionChanged="MessageTemplate_SelectionChanged">
                                <ComboBox.Style>
                                    <Style TargetType="ComboBox">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ComboBox">
                                                    <Grid>
                                                        <ToggleButton Name="ToggleButton"
                                                                     Background="{TemplateBinding Background}"
                                                                     BorderBrush="{TemplateBinding BorderBrush}"
                                                                     BorderThickness="{TemplateBinding BorderThickness}"
                                                                     Grid.Column="2"
                                                                     Focusable="false"
                                                                     IsChecked="{Binding Path=IsDropDownOpen,Mode=TwoWay,RelativeSource={RelativeSource TemplatedParent}}"
                                                                     ClickMode="Press">
                                                            <ToggleButton.Style>
                                                                <Style TargetType="ToggleButton">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="ToggleButton">
                                                                                <Border Background="{TemplateBinding Background}"
                                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                                       CornerRadius="4">
                                                                                    <Grid>
                                                                                        <Grid.ColumnDefinitions>
                                                                                            <ColumnDefinition />
                                                                                            <ColumnDefinition Width="20" />
                                                                                        </Grid.ColumnDefinitions>
                                                                                        <Path Grid.Column="1"
                                                                                             HorizontalAlignment="Center"
                                                                                             VerticalAlignment="Center"
                                                                                             Data="M 0 0 L 4 4 L 8 0 Z"
                                                                                             Fill="#666666"/>
                                                                                    </Grid>
                                                                                </Border>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </ToggleButton.Style>
                                                        </ToggleButton>
                                                        <ContentPresenter Name="ContentSite"
                                                                         IsHitTestVisible="False"
                                                                         Content="{TemplateBinding SelectionBoxItem}"
                                                                         ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                                         ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                                         Margin="10,8,30,8"
                                                                         VerticalAlignment="Center"
                                                                         HorizontalAlignment="Left" />
                                                        <TextBox x:Name="PART_EditableTextBox"
                                                                Style="{x:Null}"
                                                                Template="{DynamicResource ComboBoxTextBox}"
                                                                HorizontalAlignment="Left"
                                                                VerticalAlignment="Center"
                                                                Margin="3,3,23,3"
                                                                Focusable="True"
                                                                Background="Transparent"
                                                                Visibility="Hidden"
                                                                IsReadOnly="{TemplateBinding IsReadOnly}"/>
                                                        <Popup Name="Popup"
                                                              Placement="Bottom"
                                                              IsOpen="{TemplateBinding IsDropDownOpen}"
                                                              AllowsTransparency="True"
                                                              Focusable="False"
                                                              PopupAnimation="Slide">
                                                            <Grid Name="DropDown"
                                                                 SnapsToDevicePixels="True"
                                                                 MinWidth="{TemplateBinding ActualWidth}"
                                                                 MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                                                <Border x:Name="DropDownBorder"
                                                                       Background="White"
                                                                       BorderThickness="1"
                                                                       BorderBrush="#CCCCCC"
                                                                       CornerRadius="4"/>
                                                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                                                </ScrollViewer>
                                                            </Grid>
                                                        </Popup>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ComboBox.Style>
                            </ComboBox>
                        </StackPanel>

                        <!-- Send Options -->
                        <StackPanel Grid.Row="2" Orientation="Vertical" HorizontalAlignment="Center" Margin="0,0,0,10">
                            <!-- Send Method Selection -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                                <TextBlock Text="📤 طريقة الإرسال:" FontSize="12" FontWeight="SemiBold"
                                          Foreground="#495057" VerticalAlignment="Center" Margin="0,0,15,0"/>

                                <!-- SMS Option -->
                                <RadioButton GroupName="SendMethod" IsChecked="{Binding IsSMSSelected}"
                                            Margin="0,0,20,0" Cursor="Hand"
                                            MouseLeftButtonDown="SendOption_Click" Tag="SMS">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📱" FontSize="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="SMS" FontSize="12" FontWeight="SemiBold" Foreground="#17A2B8"/>
                                    </StackPanel>
                                </RadioButton>

                                <!-- WhatsApp Option -->
                                <RadioButton GroupName="SendMethod" IsChecked="{Binding IsWhatsAppSelected}"
                                            Margin="0,0,20,0" Cursor="Hand"
                                            MouseLeftButtonDown="SendOption_Click" Tag="WhatsApp">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="💬" FontSize="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="WhatsApp" FontSize="12" FontWeight="SemiBold" Foreground="#25D366"/>
                                    </StackPanel>
                                </RadioButton>

                                <!-- Email Option -->
                                <RadioButton GroupName="SendMethod" IsChecked="{Binding IsEmailSelected}"
                                            Cursor="Hand"
                                            MouseLeftButtonDown="SendOption_Click" Tag="Email">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📧" FontSize="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="Email" FontSize="12" FontWeight="SemiBold" Foreground="#6C757D"/>
                                    </StackPanel>
                                </RadioButton>
                            </StackPanel>

                            <!-- WhatsApp Options (only visible when WhatsApp is selected) -->
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center"
                                       Visibility="{Binding IsWhatsAppSelected, Converter={StaticResource BooleanToVisibilityConverter}}">

                                <!-- Sending Mode -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5">
                                    <TextBlock Text="🤖 نمط الإرسال:" FontSize="11" FontWeight="SemiBold"
                                              Foreground="#495057" VerticalAlignment="Center" Margin="0,0,10,0"/>

                                    <CheckBox IsChecked="{Binding UseAutomaticSending}"
                                             Content="إرسال تلقائي" FontSize="11"
                                             Foreground="#25D366" FontWeight="SemiBold"
                                             ToolTip="تفعيل الإرسال التلقائي - سيقوم النظام بالضغط على زر الإرسال تلقائياً"/>

                                    <TextBlock Text="(يدوي إذا لم يُفعل)" FontSize="10"
                                              Foreground="#6C757D" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>

                                <!-- Send Type -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5">
                                    <TextBlock Text="📤 نوع الإرسال:" FontSize="11" FontWeight="SemiBold"
                                              Foreground="#495057" VerticalAlignment="Center" Margin="0,0,15,0"/>

                                    <RadioButton GroupName="SendType" IsChecked="{Binding IsIndividualSending}"
                                                Margin="0,0,15,0" Cursor="Hand">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="👤" FontSize="14" Margin="0,0,5,0"/>
                                            <TextBlock Text="فردي" FontSize="11" FontWeight="SemiBold" Foreground="#007BFF"/>
                                        </StackPanel>
                                    </RadioButton>

                                    <RadioButton GroupName="SendType" IsChecked="{Binding IsGroupSending}"
                                                Cursor="Hand">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="👥" FontSize="14" Margin="0,0,5,0"/>
                                            <TextBlock Text="جماعي" FontSize="11" FontWeight="SemiBold" Foreground="#28A745"/>
                                        </StackPanel>
                                    </RadioButton>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>

                        <!-- Selected Drivers List -->
                        <Border Grid.Row="3" Style="{StaticResource ModernCard}" Margin="0,10,0,5"
                               Visibility="{Binding HasSelectedDrivers, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="🚗 السائقين المحددين:" FontSize="14" FontWeight="Bold"
                                          Foreground="#495057" Margin="0,0,0,10"/>

                                <ScrollViewer MaxHeight="150" VerticalScrollBarVisibility="Auto">
                                    <ItemsControl ItemsSource="{Binding SelectedDrivers}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="#F8F9FA" CornerRadius="8" Padding="10" Margin="0,2">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelectedForSending}"
                                                                 VerticalAlignment="Center" Margin="0,0,10,0"/>

                                                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                                            <TextBlock Text="👤" FontSize="12" Margin="0,0,5,0"/>
                                                            <TextBlock Text="{Binding Name}" FontWeight="SemiBold" FontSize="12"/>
                                                            <TextBlock Text=" - " FontSize="12" Foreground="#6C757D"/>
                                                            <TextBlock Text="{Binding PhoneNumber}" FontSize="11" Foreground="#6C757D"/>
                                                        </StackPanel>

                                                        <TextBlock Grid.Column="2" Text="{Binding VehicleType}"
                                                                  FontSize="10" Foreground="#28A745"
                                                                  VerticalAlignment="Center"/>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>

                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                                    <Button Content="تحديد الكل" Style="{StaticResource ProfessionalButton}"
                                           Height="30" FontSize="10" Padding="15,5"
                                           Command="{Binding SelectAllForSendingCommand}"/>
                                    <Button Content="إلغاء الكل" Style="{StaticResource DangerButton}"
                                           Height="30" FontSize="10" Padding="15,5"
                                           Command="{Binding ClearSendingSelectionCommand}"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <TextBox Grid.Row="4" Text="{Binding MessageContent, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource ModernTextBox}"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="400"
                                VerticalScrollBarVisibility="Auto"
                                FontFamily="Segoe UI" FontSize="13"
                                Margin="0,5,0,0"/>
                    </Grid>

                    <!-- Send Actions -->
                    <Grid Grid.Row="2" Margin="0,15,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" Style="{StaticResource WarningButton}" Height="35" FontSize="11"
                               Click="CopyMessage_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📋" FontSize="12" Margin="0,0,4,0"/>
                                <TextBlock Text="نسخ الرسالة"/>
                            </StackPanel>
                        </Button>

                        <!-- Unified Send Button -->
                        <Button Grid.Column="2" Grid.ColumnSpan="3" Style="{StaticResource SuccessButton}"
                               Height="40" FontSize="12" FontWeight="Bold"
                               Command="{Binding SendMessageCommand}"
                               HorizontalAlignment="Center" MinWidth="150">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🚀" FontSize="14" Margin="0,0,8,0"/>
                                <TextBlock Text="إرسال" FontWeight="Bold"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
