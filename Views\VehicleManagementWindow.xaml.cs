using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class VehicleManagementWindow : Window
    {
        public VehicleManagementWindow()
        {
            try
            {
                InitializeComponent();
                DataContext = new VehicleManagementViewModel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة إدارة السيارات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    viewModel.LoadData();
                    MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                        DefaultExt = "csv",
                        FileName = $"بيانات_السائقين_{DateTime.Now:yyyy-MM-dd_HH-mm}"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        ExportToExcel(viewModel.FilteredDrivers, saveFileDialog.FileName);

                        var result = MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{saveFileDialog.FileName}\n\nهل تريد فتح الملف الآن؟",
                                      "تصدير ناجح", MessageBoxButton.YesNo, MessageBoxImage.Information);

                        if (result == MessageBoxResult.Yes)
                        {
                            try
                            {
                                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                                {
                                    FileName = saveFileDialog.FileName,
                                    UseShellExecute = true
                                });
                            }
                            catch (Exception openEx)
                            {
                                MessageBox.Show($"تم حفظ الملف بنجاح ولكن لا يمكن فتحه تلقائياً:\n{openEx.Message}",
                                              "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Driver driver)
                {
                    var details = $"📋 تفاصيل السائق\n\n" +
                                 $"🆔 الرقم: {driver.Id}\n" +
                                 $"👤 الاسم: {driver.Name}\n" +
                                 $"📱 الهاتف: {driver.PhoneNumber}\n" +
                                 $"🆔 رقم البطاقة: {driver.CardNumber}\n" +
                                 $"📄 نوع البطاقة: {driver.CardType}\n" +
                                 $"📍 مكان الإصدار: {driver.CardIssuePlace}\n" +
                                 $"📅 تاريخ إصدار البطاقة: {driver.CardIssueDate:yyyy-MM-dd}\n" +
                                 $"🚗 نوع المركبة: {driver.VehicleType}\n" +
                                 $"🔢 رقم المركبة: {driver.VehicleNumber}\n" +
                                 $"🎨 لون المركبة: {driver.VehicleColor}\n" +
                                 $"⚡ قدرة المركبة: {driver.VehicleCapacity}\n" +
                                 $"🏷️ موديل المركبة: {driver.VehicleModel}\n" +
                                 $"🪪 رقم الرخصة: {driver.LicenseNumber}\n" +
                                 $"📅 تاريخ إصدار الرخصة: {driver.LicenseIssueDate:yyyy-MM-dd}\n" +
                                 $"📝 كود السائق: {driver.DriverCode}\n" +
                                 $"📊 الحالة: {driver.Status}\n" +
                                 $"📝 ملاحظات: {driver.Notes}";

                    MessageBox.Show(details, "تفاصيل السائق", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Driver driver)
                {
                    if (DataContext is VehicleManagementViewModel viewModel)
                    {
                        viewModel.SelectedDriver = driver;
                        MessageBox.Show($"تم تحديد السائق: {driver.Name}\nيمكنك الآن تعديل البيانات في النموذج أعلاه والضغط على 'حفظ التعديلات'",
                                      "تحديد للتعديل", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد السائق للتعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteSelectedDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel && viewModel.SelectedDriver != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف السائق: {viewModel.SelectedDriver.Name}؟\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (viewModel.DeleteCommand?.CanExecute() == true)
                        {
                            viewModel.DeleteCommand.Execute();
                            MessageBox.Show("تم حذف السائق بنجاح", "حذف ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سائق من القائمة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السائق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsMenu = new System.Windows.Controls.ContextMenu();

                var refreshMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "🔄 تحديث البيانات",
                    ToolTip = "إعادة تحميل جميع البيانات من قاعدة البيانات"
                };
                refreshMenuItem.Click += RefreshDataButton_Click;

                var exportMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "📊 تصدير إلى Excel",
                    ToolTip = "تصدير البيانات المعروضة إلى ملف Excel"
                };
                exportMenuItem.Click += ExportToExcelButton_Click;

                var clearFilterMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "🔍 مسح الفلاتر",
                    ToolTip = "إزالة جميع الفلاتر وعرض كامل البيانات"
                };
                clearFilterMenuItem.Click += (s, args) => {
                    if (DataContext is VehicleManagementViewModel vm)
                    {
                        vm.ClearFilterCommand?.Execute();
                    }
                };

                settingsMenu.Items.Add(refreshMenuItem);
                settingsMenu.Items.Add(exportMenuItem);
                settingsMenu.Items.Add(new System.Windows.Controls.Separator());
                settingsMenu.Items.Add(clearFilterMenuItem);

                settingsMenu.IsOpen = true;
                settingsMenu.PlacementTarget = sender as Button;
                settingsMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.Top;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح نافذة إضافة سائق جديد محسنة
        /// </summary>
        private void AddNewDriverButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    // مسح النموذج للإدخال الجديد
                    viewModel.SelectedDriver = new Driver();

                    var result = MessageBox.Show(
                        "✨ إضافة سائق جديد\n\n" +
                        "سيتم مسح النموذج أعلاه لإدخال بيانات سائق جديد.\n" +
                        "املأ جميع البيانات المطلوبة ثم اضغط 'حفظ التعديلات'.\n\n" +
                        "هل تريد المتابعة؟",
                        "إضافة سائق جديد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // تحديث الإحصائيات
                        UpdateQuickStats();

                        MessageBox.Show(
                            "✅ تم تجهيز النموذج لإضافة سائق جديد\n\n" +
                            "📝 املأ البيانات في النموذج أعلاه\n" +
                            "💾 اضغط 'حفظ التعديلات' عند الانتهاء",
                            "جاهز للإدخال",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تجهيز نموذج السائق الجديد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات السريعة في الهيدر
        /// </summary>
        private void UpdateQuickStats()
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    var totalDrivers = viewModel.FilteredDrivers?.Count ?? 0;
                    var activeDrivers = viewModel.FilteredDrivers?.Count(d => d.IsActive) ?? 0;

                    // يمكن إضافة عرض الإحصائيات لاحقاً
                    System.Diagnostics.Debug.WriteLine($"📊 المجموع: {totalDrivers} | النشط: {activeDrivers}");
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء الإحصائيات
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح شاشة إدارة السائقين الاحترافية
        /// </summary>
        private void OpenProfessionalDriverManagementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚗 Opening Professional Driver Management Window");

                // فتح شاشة إدارة السائقين الاحترافية
                var driverManagementWindow = new Views.ProfessionalDriverManagementWindow();
                var result = driverManagementWindow.ShowDialog();

                if (result == true)
                {
                    // تحديث البيانات بعد إغلاق الشاشة
                    System.Diagnostics.Debug.WriteLine("✅ Driver management completed, refreshing data");

                    if (DataContext is VehicleManagementViewModel vm)
                    {
                        vm.LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error opening professional driver management: {ex.Message}");
                MessageBox.Show($"❌ خطأ في فتح شاشة إدارة السائقين:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إدراج البيانات الأولية للسائقين
        /// </summary>
        private async void ImportInitialDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من إدراج البيانات الأولية للسائقين؟\n\n" +
                    "سيتم إضافة 31 سائق مع جميع بياناتهم الكاملة.\n" +
                    "هذه العملية لن تؤثر على البيانات الموجودة مسبقاً.",
                    "تأكيد إدراج البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر التحميل
                    var loadingWindow = new Window
                    {
                        Title = "جاري إدراج البيانات...",
                        Width = 300,
                        Height = 150,
                        WindowStartupLocation = WindowStartupLocation.CenterOwner,
                        Owner = this,
                        ResizeMode = ResizeMode.NoResize,
                        Content = new StackPanel
                        {
                            Margin = new Thickness(20),
                            Children =
                            {
                                new TextBlock
                                {
                                    Text = "🔄 جاري إدراج بيانات السائقين...",
                                    FontSize = 14,
                                    HorizontalAlignment = HorizontalAlignment.Center,
                                    Margin = new Thickness(0, 20, 0, 20)
                                },
                                new ProgressBar
                                {
                                    IsIndeterminate = true,
                                    Height = 20
                                }
                            }
                        }
                    };

                    loadingWindow.Show();

                    try
                    {
                        // إدراج البيانات
                        using var context = new Data.ApplicationDbContext();
                        await Data.SeedDriversData.SeedDriversAsync(context);

                        loadingWindow.Close();

                        // إعادة تحميل البيانات
                        if (DataContext is VehicleManagementViewModel viewModel)
                        {
                            viewModel.LoadData();
                        }

                        MessageBox.Show(
                            "✅ تم إدراج بيانات السائقين بنجاح!\n\n" +
                            "تم إضافة 31 سائق مع جميع بياناتهم الكاملة.\n" +
                            "يمكنك الآن رؤية البيانات في الجدول.",
                            "نجح الإدراج",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        loadingWindow.Close();

                        if (ex.Message.Contains("يوجد") && ex.Message.Contains("سائق في قاعدة البيانات"))
                        {
                            MessageBox.Show(
                                "ℹ️ البيانات موجودة بالفعل!\n\n" +
                                "يوجد سائقين في قاعدة البيانات مسبقاً.\n" +
                                "لن يتم إضافة بيانات مكررة.",
                                "البيانات موجودة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show($"❌ خطأ في إدراج البيانات: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عملية الإدراج: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcel(ObservableCollection<Driver> drivers, string filePath)
        {
            try
            {
                var csv = new StringBuilder();

                // Add BOM for proper Arabic display in Excel
                csv.Append('\uFEFF');

                // Add headers in Arabic
                csv.AppendLine("الرقم,اسم السائق,رقم الهاتف,نوع المركبة,رقم المركبة,الحالة,رقم البطاقة,نوع البطاقة,مكان إصدار البطاقة,تاريخ إصدار البطاقة,رقم الرخصة,تاريخ إصدار الرخصة,لون المركبة,قدرة المركبة,موديل المركبة,كود السائق,ملاحظات");

                // Add data rows
                foreach (var driver in drivers)
                {
                    var row = $"{driver.Id}," +
                             $"\"{EscapeCsvField(driver.Name)}\"," +
                             $"\"{EscapeCsvField(driver.PhoneNumber)}\"," +
                             $"\"{EscapeCsvField(driver.VehicleType)}\"," +
                             $"\"{EscapeCsvField(driver.VehicleNumber)}\"," +
                             $"\"{EscapeCsvField(driver.Status)}\"," +
                             $"\"{EscapeCsvField(driver.CardNumber)}\"," +
                             $"\"{EscapeCsvField(driver.CardType)}\"," +
                             $"\"{EscapeCsvField(driver.CardIssuePlace)}\"," +
                             $"{driver.CardIssueDate:yyyy-MM-dd}," +
                             $"\"{EscapeCsvField(driver.LicenseNumber)}\"," +
                             $"{driver.LicenseIssueDate:yyyy-MM-dd}," +
                             $"\"{EscapeCsvField(driver.VehicleColor)}\"," +
                             $"\"{EscapeCsvField(driver.VehicleCapacity)}\"," +
                             $"\"{EscapeCsvField(driver.VehicleModel)}\"," +
                             $"\"{EscapeCsvField(driver.DriverCode)}\"," +
                             $"\"{EscapeCsvField(driver.Notes)}\"";

                    csv.AppendLine(row);
                }

                // Write to file with UTF-8 encoding to support Arabic
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في كتابة الملف: {ex.Message}");
            }
        }

        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // Escape quotes by doubling them
            return field.Replace("\"", "\"\"");
        }

        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("🔍 البحث المتقدم\n\nيمكنك استخدام مربعات البحث في أعلى الجدول للبحث السريع.\nسيتم إضافة المزيد من خيارات البحث قريباً.",
                              "البحث المتقدم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StatisticsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is VehicleManagementViewModel viewModel)
                {
                    var totalDrivers = viewModel.FilteredDrivers.Count;
                    var activeDrivers = viewModel.FilteredDrivers.Count(d => d.Status == "نشط");
                    var inactiveDrivers = viewModel.FilteredDrivers.Count(d => d.Status == "غير نشط");
                    var vehicleTypes = viewModel.FilteredDrivers.GroupBy(d => d.VehicleType)
                                                               .Where(g => !string.IsNullOrEmpty(g.Key))
                                                               .Select(g => $"• {g.Key}: {g.Count()} سائق")
                                                               .ToList();

                    var statistics = $"📊 إحصائيات السائقين\n\n" +
                                   $"👥 إجمالي السائقين: {totalDrivers}\n" +
                                   $"✅ السائقين النشطين: {activeDrivers}\n" +
                                   $"❌ السائقين غير النشطين: {inactiveDrivers}\n\n" +
                                   $"🚗 توزيع أنواع المركبات:\n" +
                                   (vehicleTypes.Any() ? string.Join("\n", vehicleTypes.Take(8)) : "لا توجد بيانات") +
                                   (vehicleTypes.Count > 8 ? $"\n... و {vehicleTypes.Count - 8} نوع آخر" : "");

                    MessageBox.Show(statistics, "إحصائيات مفصلة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
