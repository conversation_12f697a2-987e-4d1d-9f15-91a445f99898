using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد لإضافة عمود VisitId إلى قاعدة البيانات
    /// </summary>
    public static class VisitIdMigrationHelper
    {
        /// <summary>
        /// تطبيق Migration لإضافة عمود VisitId
        /// </summary>
        public static async Task ApplyVisitIdMigrationAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة عمود VisitId...");

                // إضافة عمود VisitId إلى جدول FieldVisits
                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[FieldVisits]') AND name = 'VisitId')
                    BEGIN
                        ALTER TABLE [FieldVisits] ADD [VisitId] int NOT NULL DEFAULT 0;
                        PRINT 'تم إضافة عمود VisitId إلى جدول FieldVisits';
                    END
                    ELSE
                    BEGIN
                        PRINT 'عمود VisitId موجود بالفعل في جدول FieldVisits';
                    END
                ");

                // إضافة عمود VisitId إلى جدول DriverQuotes
                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'VisitId')
                    BEGIN
                        ALTER TABLE [DriverQuotes] ADD [VisitId] int NOT NULL DEFAULT 0;
                        PRINT 'تم إضافة عمود VisitId إلى جدول DriverQuotes';
                    END
                    ELSE
                    BEGIN
                        PRINT 'عمود VisitId موجود بالفعل في جدول DriverQuotes';
                    END
                ");

                // تحديث VisitId في جدول FieldVisits ليكون مساوياً لـ Id
                await context.Database.ExecuteSqlRawAsync(@"
                    UPDATE [FieldVisits] 
                    SET [VisitId] = [Id] 
                    WHERE [VisitId] = 0 OR [VisitId] IS NULL;
                    PRINT 'تم تحديث VisitId في جدول FieldVisits';
                ");

                // تحديث VisitId في جدول DriverQuotes بناءً على VisitNumber
                await context.Database.ExecuteSqlRawAsync(@"
                    UPDATE dq 
                    SET dq.[VisitId] = fv.[VisitId]
                    FROM [DriverQuotes] dq
                    INNER JOIN [FieldVisits] fv ON dq.[VisitNumber] = fv.[VisitNumber]
                    WHERE dq.[VisitId] = 0 OR dq.[VisitId] IS NULL;
                    PRINT 'تم تحديث VisitId في جدول DriverQuotes';
                ");

                // إنشاء فهرس على VisitId لتحسين الأداء
                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_DriverQuotes_VisitId')
                    BEGIN
                        CREATE INDEX IX_DriverQuotes_VisitId ON [DriverQuotes] ([VisitId]);
                        PRINT 'تم إنشاء فهرس على VisitId في جدول DriverQuotes';
                    END
                ");

                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FieldVisits_VisitId')
                    BEGIN
                        CREATE INDEX IX_FieldVisits_VisitId ON [FieldVisits] ([VisitId]);
                        PRINT 'تم إنشاء فهرس على VisitId في جدول FieldVisits';
                    END
                ");

                System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود VisitId بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة عمود VisitId: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود عمود VisitId
        /// </summary>
        public static async Task<bool> CheckVisitIdColumnExistsAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                var result = await context.Database.ExecuteSqlRawAsync(@"
                    SELECT COUNT(*) FROM sys.columns 
                    WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'VisitId'
                ");

                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من عمود VisitId: {ex.Message}");
                return false;
            }
        }
    }
}
