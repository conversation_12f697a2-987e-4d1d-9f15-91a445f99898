<Window x:Class="DriverManagementSystem.Views.SectorManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="القطاعات والضباط"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Professional Button Style -->
            <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
                <Setter Property="Height" Value="40"/>
                <Setter Property="MinWidth" Value="100"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Bold"/>
            </Style>
            
            <!-- Header Style -->
            <Style x:Key="HeaderStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="0,0,10,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="القطاعــــــــــات" Style="{StaticResource HeaderStyle}"/>
                
                <Button Grid.Column="1"
                        Content="الرئيسية"
                        Background="#FF9800"
                        Foreground="White"
                        BorderBrush="Transparent"
                        Padding="20,10"
                        Margin="20"

                        FontWeight="Bold"
                        Click="CloseWindow_Click"/>
            </Grid>
        </Border>

        <!-- Sector Selection -->
        <Border Grid.Row="1" Background="#37474F" Margin="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                          Text="القطــاع" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Foreground="White"
                          VerticalAlignment="Center"
                          Margin="20,0"/>
                
                <ComboBox Grid.Column="1"
                         x:Name="SectorComboBox"
                         ItemsSource="{Binding Sectors}"
                         SelectedItem="{Binding SelectedSector}"
                         DisplayMemberPath="Name"
                         Height="35"
                         Margin="10"
                         FontSize="14"/>
                

            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Margin="10" Padding="10"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Form Section -->
                <Border Grid.Row="0" Background="#37474F" Padding="20" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Labels -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الضابط" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="الصفة" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="رقم التلفون" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="رقم البطاقة" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                        <TextBlock Grid.Row="0" Grid.Column="4" Text="نوع البطاقة" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>
                        <TextBlock Grid.Row="0" Grid.Column="5" Text="الكود" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" Margin="5"/>

                        <!-- Input Fields -->
                        <TextBox Grid.Row="1" Grid.Column="0" 
                                Text="{Binding OfficerName, UpdateSourceTrigger=PropertyChanged}"
                                Height="35" Margin="5" FontSize="12" TextAlignment="Center"/>
                        
                        <ComboBox Grid.Row="1" Grid.Column="1"
                                 ItemsSource="{Binding Ranks}"
                                 SelectedItem="{Binding SelectedRank}"
                                 Height="35" Margin="5" FontSize="12"/>
                        
                        <TextBox Grid.Row="1" Grid.Column="2"
                                Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                Height="35" Margin="5" FontSize="12" TextAlignment="Center"/>
                        
                        <TextBox Grid.Row="1" Grid.Column="3"
                                Text="{Binding CardNumber, UpdateSourceTrigger=PropertyChanged}"
                                Height="35" Margin="5" FontSize="12" TextAlignment="Center"/>
                        
                        <ComboBox Grid.Row="1" Grid.Column="4"
                                 ItemsSource="{Binding CardTypes}"
                                 SelectedItem="{Binding SelectedCardType}"
                                 Height="35" Margin="5" FontSize="12"/>
                        
                        <TextBox Grid.Row="1" Grid.Column="5"
                                Text="{Binding Code, UpdateSourceTrigger=PropertyChanged}"
                                Height="35" Margin="5" FontSize="12" TextAlignment="Center"/>
                    </Grid>
                </Border>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1"
                         ItemsSource="{Binding Officers}"
                         SelectedItem="{Binding SelectedOfficer}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         GridLinesVisibility="All"
                         HeadersVisibility="Column"
                         Background="White"
                         AlternatingRowBackground="#F5F5F5"
                         FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الضابط" Binding="{Binding Name}" Width="*" />
                        <DataGridTextColumn Header="الصفة" Binding="{Binding Rank}" Width="120" />
                        <DataGridTextColumn Header="رقم التلفون" Binding="{Binding PhoneNumber}" Width="120" />
                        <DataGridTextColumn Header="رقم البطاقة" Binding="{Binding CardNumber}" Width="140" />
                        <DataGridTextColumn Header="نوع البطاقة" Binding="{Binding CardType}" Width="120" />
                        <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="80" />
                    </DataGrid.Columns>
                    
                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Height" Value="35"/>
                        </Style>
                    </DataGrid.RowStyle>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="3" Background="#37474F" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,-20,0,-10">
                <Button Content="إغلاق"
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#F44336" Foreground="White" BorderBrush="Transparent"
                       Click="CloseWindow_Click"/>

                <Button Content="حذف"
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#FF9800" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding DeleteCommand}"/>

                <Button Content="إضافة"
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#4CAF50" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding AddCommand}"/>

                <Button Content="حفظ"
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#2196F3" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding SaveCommand}"/>

                <!-- Navigation Buttons -->
                <Button Content="◀"
                       Height="40" Width="40" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#607D8B" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding PreviousCommand}"/>

                <Button Content="▶"
                       Height="40" Width="40" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#607D8B" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding NextCommand}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
