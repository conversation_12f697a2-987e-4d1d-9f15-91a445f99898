<Window x:Class="DriverManagementSystem.Views.ReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:DriverManagementSystem.Views"
        Title="تقرير الزيارة الميدانية"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}" Margin="0,0,0,-6">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📄" FontSize="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock 
                             FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="تقرير النزول الميداني"/></TextBlock>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="📱 توثيق الرسائل"
                            Click="MessageDocumentationButton_Click"
                            Background="#17A2B8"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#138496"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="📋 التكليف"
                            Click="AssignmentButton_Click"
                            Background="#FF5E5E6B"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#218838"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- زر تشخيص عدد الأيام -->

                    <!-- زر الطباعة الجديد المتطور -->
                    <Button Content="❌ إغلاق"
                            Click="CloseButton_Click"
                            Background="#DC3545"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#C82333"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Visit Selection -->
            <Border Grid.Column="0" Background="White" Margin="10" Padding="15" CornerRadius="8">
                <StackPanel>
                    <TextBlock Text="📋 اختيار الزيارة الميدانية"
                             FontSize="16" FontWeight="Bold"
                             Margin="0,0,0,15"/>

                    <DataGrid ItemsSource="{Binding FieldVisits}"
                            SelectedItem="{Binding SelectedVisit}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            SelectionMode="Single"
                            Height="500">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الزيارة"
                                              Binding="{Binding VisitNumber}"
                                              Width="80"/>
                            <DataGridTextColumn Header="المهمة"
                                              Binding="{Binding MissionPurpose}"
                                              Width="*"/>
                            <DataGridTextColumn Header="التاريخ"
                                              Binding="{Binding DepartureDate, StringFormat=dd/MM/yyyy}"
                                              Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>




                </StackPanel>
            </Border>

            <!-- Right Panel - Report Content -->
            <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto" Margin="10">
                <Border Background="White" Padding="20" CornerRadius="8">
                    <local:ReportView DataContext="{Binding}"/>
                </Border>
            </ScrollViewer>
        </Grid>
    </Grid>
</Window>
