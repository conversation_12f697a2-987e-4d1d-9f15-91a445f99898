<Window x:Class="DriverManagementSystem.Views.VehicleManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة السيارات والسائقين" 
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid Margin="0,0,0,-51">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="0,0,10,10">
            <Grid>
                <TextBlock Text="🚗 إدارة السيارات والسائقين"
                          FontSize="22" FontWeight="Bold" Foreground="White"
                          HorizontalAlignment="Left" VerticalAlignment="Center" Margin="50,0,0,0"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Driver Information Section -->
                <Border Grid.Row="0" Background="White" CornerRadius="8" Margin="5" Padding="15"
                        BorderBrush="#E0E0E0" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>

                    <StackPanel>
                        <TextBlock Text="بيانات السائق" FontSize="18" FontWeight="Bold"
                                  Foreground="#2196F3" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                <TextBlock Text="اسم مالك السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding DriverName, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم التلفون" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                <TextBlock Text="رقم البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding CardNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                <TextBlock Text="نوع البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding CardTypes}"
                                         SelectedItem="{Binding SelectedCardType}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                <TextBlock Text="مكان الإصدار للبطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding CardIssuePlace, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                <TextBlock Text="تاريخ اصدار البطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <DatePicker SelectedDate="{Binding CardIssueDate}"
                                           Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 3 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                <TextBlock Text="كود السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding DriverCode, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12" IsReadOnly="True" Background="#F5F5F5"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                <TextBlock Text="حالة السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ToggleButton IsChecked="{Binding IsActive, UpdateSourceTrigger=PropertyChanged}"
                                             Height="35" FontSize="12" FontWeight="Bold">
                                    <ToggleButton.Style>
                                        <Style TargetType="ToggleButton">
                                            <Setter Property="Content" Value="غير نشط"/>
                                            <Setter Property="Background" Value="#F44336"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="BorderBrush" Value="Transparent"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsChecked" Value="True">
                                                    <Setter Property="Content" Value="نشط"/>
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ToggleButton.Style>
                                </ToggleButton>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Vehicle Information Section -->
                <Border Grid.Row="1" Background="White" CornerRadius="8" Margin="5" Padding="15"
                        BorderBrush="#E0E0E0" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>

                    <StackPanel>
                        <TextBlock Text="بيانات السيارة" FontSize="18" FontWeight="Bold"
                                  Foreground="#2196F3" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                <TextBlock Text="نوع السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding VehicleTypes}"
                                         SelectedItem="{Binding SelectedVehicleType}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding VehicleNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                <TextBlock Text="موديل السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding VehicleModel, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                <TextBlock Text="قدرة السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding VehicleCapacities}"
                                         SelectedItem="{Binding SelectedVehicleCapacity}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم الرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding LicenseNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                <TextBlock Text="تاريخ الاصدار للرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <DatePicker SelectedDate="{Binding LicenseIssueDate}"
                                           Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 3 - Additional Fields -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                <TextBlock Text="لون السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding VehicleColors}"
                                         SelectedItem="{Binding SelectedVehicleColor}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="5">
                                <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                        Height="60" FontSize="12" TextWrapping="Wrap"
                                        AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Data Grid with Filter -->
                <Border Grid.Row="2" Background="White" CornerRadius="8" Margin="5" Padding="10"
                        BorderBrush="#E0E0E0" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header with Statistics -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="قائمة السيارات والسائقين" FontSize="18" FontWeight="Bold"
                                      Foreground="#2196F3" VerticalAlignment="Center"/>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBlock Text="إجمالي السائقين: " FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding TotalDriversCount}" FontWeight="Bold" Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,15,0"/>
                                <TextBlock Text="المعروض: " FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding FilteredDriversCount}" FontWeight="Bold" Foreground="#2196F3" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- Filter Section -->

                        <!-- Drivers Data Grid Section -->
                        <Border Grid.Row="2" Background="White" CornerRadius="15" Padding="25" Margin="0,10,0,0">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="320" ShadowDepth="3" Opacity="0.3"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <TextBlock Grid.Row="0" Text="قائمة السائقين والمركبات" FontSize="18" FontWeight="Bold"
                                          Foreground="#2C3E50" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                                <!-- Data Grid -->
                                <DataGrid Grid.Row="1" x:Name="DriversDataGrid"
                                         ItemsSource="{Binding FilteredDrivers}"
                                         SelectedItem="{Binding SelectedDriver}"
                                         AutoGenerateColumns="False"
                                         CanUserAddRows="False"
                                         CanUserDeleteRows="False"
                                         GridLinesVisibility="Horizontal"
                                         HeadersVisibility="Column"
                                         SelectionMode="Single"
                                         AlternatingRowBackground="#F8F9FA"
                                         RowBackground="White"
                                         FontSize="12"
                                         Height="300"
                                         Margin="0,0,0,20">

                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="م" Binding="{Binding Id}" Width="50" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="اسم السائق" Binding="{Binding Name}" Width="140" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="110" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleType}" Width="90" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="رقم المركبة" Binding="{Binding VehicleNumber}" Width="110" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="رقم البطاقة" Binding="{Binding CardNumber}" Width="110" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="لون المركبة" Binding="{Binding VehicleColor}" Width="90" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="قدرة المركبة" Binding="{Binding VehicleCapacity}" Width="90" IsReadOnly="True"/>

                                        <!-- Action Buttons Column -->
                                        <DataGridTemplateColumn Header="الإجراءات" Width="120" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <Button Content="👁️" Width="30" Height="25" Margin="2"
                                                               Background="#17A2B8" Foreground="White" BorderThickness="0"
                                                               ToolTip="عرض تفاصيل السائق" FontSize="10"
                                                               Click="ViewDriverButton_Click" Tag="{Binding}"/>
                                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                               Background="#FFC107" Foreground="#212529" BorderThickness="0"
                                                               ToolTip="تعديل بيانات السائق" FontSize="10"
                                                               Click="EditDriverButton_Click" Tag="{Binding}"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>

                                    <DataGrid.RowStyle>
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="Height" Value="35"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#2196F3"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                </Trigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGrid.RowStyle>

                                    <DataGrid.ColumnHeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#37474F"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Height" Value="40"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGrid.ColumnHeaderStyle>
                                </DataGrid>

                                <!-- Statistics Summary -->

                                <!-- Quick Actions -->
                            </Grid>
                        </Border>
                    </Grid>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1" Background="Transparent" Padding="15" Margin="0,599,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="677,-693,0,671">
                <Button Content="➕ إضافة سائق"
                       Height="45" MinWidth="130" Margin="8" FontSize="14" FontWeight="Bold"
                       Background="#28A745" Foreground="White" BorderBrush="Transparent"
                       Click="AddNewDriverButton_Click"
                       ToolTip="إضافة سائق جديد"/>

                <Button Content="💾 حفظ البيانات"
                       Height="45" MinWidth="130" Margin="8" FontSize="14" FontWeight="Bold"
                       Background="#007BFF" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding SaveCommand}"
                       ToolTip="حفظ البيانات المدخلة"/>

                <Button Content="🗑️ حذف السائق"
                       Height="45" MinWidth="130" Margin="8" FontSize="14" FontWeight="Bold"
                       Background="#DC3545" Foreground="White" BorderBrush="Transparent"
                       Click="DeleteSelectedDriverButton_Click"
                       ToolTip="حذف السائق المحدد"/>

                <Button Content="📊 تصدير Excel"
                       Height="45" MinWidth="130" Margin="8" FontSize="14" FontWeight="Bold"
                       Background="#17A2B8" Foreground="White" BorderBrush="Transparent"
                       Click="ExportToExcelButton_Click"
                       ToolTip="تصدير البيانات إلى Excel"/>

                <Button Content="إغلاق"
                       Height="45" MinWidth="100" Margin="8" FontSize="14" FontWeight="Bold"
                       Background="#6C757D" Foreground="White" BorderBrush="Transparent"
                       Click="CloseWindow_Click"
                       ToolTip="إغلاق النافذة"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
