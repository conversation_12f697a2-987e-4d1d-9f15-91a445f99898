using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إضافة عمود VisitId لربط السائقين بالزيارات بشكل فريد
    /// </summary>
    public class AddVisitIdColumn
    {
        private readonly ApplicationDbContext _context;

        public AddVisitIdColumn(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// تطبيق التحديث - إضافة عمود VisitId
        /// </summary>
        public async Task ApplyAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة عمود VisitId...");

                // إضافة عمود VisitId إلى جدول FieldVisits
                await _context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[FieldVisits]') AND name = 'VisitId')
                    BEGIN
                        ALTER TABLE [FieldVisits] ADD [VisitId] int NOT NULL DEFAULT 0;
                        PRINT 'تم إضافة عمود VisitId إلى جدول FieldVisits';
                    END
                    ELSE
                    BEGIN
                        PRINT 'عمود VisitId موجود بالفعل في جدول FieldVisits';
                    END
                ");

                // إضافة عمود VisitId إلى جدول DriverQuotes
                await _context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'VisitId')
                    BEGIN
                        ALTER TABLE [DriverQuotes] ADD [VisitId] int NOT NULL DEFAULT 0;
                        PRINT 'تم إضافة عمود VisitId إلى جدول DriverQuotes';
                    END
                    ELSE
                    BEGIN
                        PRINT 'عمود VisitId موجود بالفعل في جدول DriverQuotes';
                    END
                ");

                // تحديث VisitId في جدول FieldVisits ليكون مساوياً لـ Id
                await _context.Database.ExecuteSqlRawAsync(@"
                    UPDATE [FieldVisits] 
                    SET [VisitId] = [Id] 
                    WHERE [VisitId] = 0 OR [VisitId] IS NULL;
                    PRINT 'تم تحديث VisitId في جدول FieldVisits';
                ");

                // تحديث VisitId في جدول DriverQuotes بناءً على VisitNumber
                await _context.Database.ExecuteSqlRawAsync(@"
                    UPDATE dq 
                    SET dq.[VisitId] = fv.[VisitId]
                    FROM [DriverQuotes] dq
                    INNER JOIN [FieldVisits] fv ON dq.[VisitNumber] = fv.[VisitNumber]
                    WHERE dq.[VisitId] = 0 OR dq.[VisitId] IS NULL;
                    PRINT 'تم تحديث VisitId في جدول DriverQuotes';
                ");

                // إنشاء فهرس على VisitId لتحسين الأداء
                await _context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_DriverQuotes_VisitId')
                    BEGIN
                        CREATE INDEX IX_DriverQuotes_VisitId ON [DriverQuotes] ([VisitId]);
                        PRINT 'تم إنشاء فهرس على VisitId في جدول DriverQuotes';
                    END
                ");

                await _context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FieldVisits_VisitId')
                    BEGIN
                        CREATE INDEX IX_FieldVisits_VisitId ON [FieldVisits] ([VisitId]);
                        PRINT 'تم إنشاء فهرس على VisitId في جدول FieldVisits';
                    END
                ");

                System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود VisitId بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة عمود VisitId: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التراجع عن التحديث - إزالة عمود VisitId
        /// </summary>
        public async Task RevertAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إزالة عمود VisitId...");

                // إزالة الفهارس أولاً
                await _context.Database.ExecuteSqlRawAsync(@"
                    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_DriverQuotes_VisitId')
                    BEGIN
                        DROP INDEX IX_DriverQuotes_VisitId ON [DriverQuotes];
                        PRINT 'تم حذف فهرس VisitId من جدول DriverQuotes';
                    END
                ");

                await _context.Database.ExecuteSqlRawAsync(@"
                    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FieldVisits_VisitId')
                    BEGIN
                        DROP INDEX IX_FieldVisits_VisitId ON [FieldVisits];
                        PRINT 'تم حذف فهرس VisitId من جدول FieldVisits';
                    END
                ");

                // إزالة العمود من DriverQuotes
                await _context.Database.ExecuteSqlRawAsync(@"
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'VisitId')
                    BEGIN
                        ALTER TABLE [DriverQuotes] DROP COLUMN [VisitId];
                        PRINT 'تم حذف عمود VisitId من جدول DriverQuotes';
                    END
                ");

                // إزالة العمود من FieldVisits
                await _context.Database.ExecuteSqlRawAsync(@"
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[FieldVisits]') AND name = 'VisitId')
                    BEGIN
                        ALTER TABLE [FieldVisits] DROP COLUMN [VisitId];
                        PRINT 'تم حذف عمود VisitId من جدول FieldVisits';
                    END
                ");

                System.Diagnostics.Debug.WriteLine("✅ تم إزالة عمود VisitId بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إزالة عمود VisitId: {ex.Message}");
                throw;
            }
        }
    }
}
