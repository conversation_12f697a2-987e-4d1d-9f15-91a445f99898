<Window x:Class="DriverManagementSystem.Views.PrintSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات الطباعة" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15" Margin="0,0,0,20">
            <TextBlock Text="🖨️ إعدادات الطباعة" FontSize="18" FontWeight="Bold" 
                       Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- اختيار الطابعة -->
                <GroupBox Header="اختيار الطابعة" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="الطابعة:" FontWeight="Bold" Margin="0,0,0,10"/>
                        <ComboBox Grid.Row="1" Name="PrinterComboBox" Margin="0,0,0,10" 
                                  SelectionChanged="PrinterComboBox_SelectionChanged"/>
                        <TextBlock Grid.Row="2" Name="PrinterStatusText" Text="حالة الطابعة: جاهزة" 
                                   Foreground="Green" FontSize="12"/>
                    </Grid>
                </GroupBox>

                <!-- إعدادات الصفحة -->
                <GroupBox Header="إعدادات الصفحة" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- حجم الورق -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="حجم الورق:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" Name="PaperSizeComboBox" Margin="0,0,0,10">
                            <ComboBoxItem Content="A4 (210 × 297 مم)" IsSelected="True" Tag="A4"/>
                            <ComboBoxItem Content="A3 (297 × 420 مم)" Tag="A3"/>
                            <ComboBoxItem Content="Letter (216 × 279 مم)" Tag="Letter"/>
                        </ComboBox>

                        <!-- اتجاه الصفحة -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="اتجاه الصفحة:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <RadioButton Name="PortraitRadio" Content="عمودي (Portrait)" IsChecked="True" 
                                         Margin="0,0,20,0" VerticalAlignment="Center"/>
                            <RadioButton Name="LandscapeRadio" Content="أفقي (Landscape)" 
                                         VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- جودة الطباعة -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="جودة الطباعة:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" Name="QualityComboBox" Margin="0,0,0,10">
                            <ComboBoxItem Content="عالية (600 DPI)" IsSelected="True" Tag="High"/>
                            <ComboBoxItem Content="متوسطة (300 DPI)" Tag="Medium"/>
                            <ComboBoxItem Content="مسودة (150 DPI)" Tag="Draft"/>
                        </ComboBox>

                        <!-- نوع الورق -->
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="نوع الورق:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Row="3" Grid.Column="1" Name="PaperTypeComboBox">
                            <ComboBoxItem Content="ورق عادي" IsSelected="True" Tag="Plain"/>
                            <ComboBoxItem Content="ورق صور" Tag="Photo"/>
                            <ComboBoxItem Content="ورق سميك" Tag="Cardstock"/>
                        </ComboBox>
                    </Grid>
                </GroupBox>

                <!-- إعدادات الطباعة -->
                <GroupBox Header="إعدادات الطباعة" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- عدد النسخ -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="عدد النسخ:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBox Name="CopiesTextBox" Text="1" Width="60" VerticalAlignment="Center" 
                                     Margin="0,0,10,0" TextAlignment="Center"/>
                            <TextBlock Text="نسخة" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- نطاق الصفحات -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="نطاق الصفحات:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,0,10">
                            <RadioButton Name="AllPagesRadio" Content="جميع الصفحات" IsChecked="True" 
                                         Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <RadioButton Name="PageRangeRadio" Content="من صفحة" Margin="0,0,10,0"/>
                                <TextBox Name="FromPageTextBox" Width="40" Text="1" IsEnabled="False" 
                                         TextAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="إلى" Margin="0,0,5,0"/>
                                <TextBox Name="ToPageTextBox" Width="40" Text="1" IsEnabled="False" 
                                         TextAlignment="Center"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- طباعة ملونة -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="الألوان:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <RadioButton Name="ColorRadio" Content="ملونة" IsChecked="True" 
                                         Margin="0,0,20,0" VerticalAlignment="Center"/>
                            <RadioButton Name="BlackWhiteRadio" Content="أبيض وأسود" 
                                         VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- طباعة على الوجهين -->
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="طباعة على الوجهين:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" Name="DuplexCheckBox" 
                                  Content="تمكين الطباعة على الوجهين" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>

                <!-- الهوامش -->
                <GroupBox Header="الهوامش (مم)" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- العلوي -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                            <TextBlock Text="العلوي:" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBox Name="TopMarginTextBox" Text="25" TextAlignment="Center"/>
                        </StackPanel>

                        <!-- الأيمن -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                            <TextBlock Text="الأيمن:" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBox Name="RightMarginTextBox" Text="25" TextAlignment="Center"/>
                        </StackPanel>

                        <!-- السفلي -->
                        <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                            <TextBlock Text="السفلي:" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBox Name="BottomMarginTextBox" Text="25" TextAlignment="Center"/>
                        </StackPanel>

                        <!-- الأيسر -->
                        <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                            <TextBlock Text="الأيسر:" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBox Name="LeftMarginTextBox" Text="25" TextAlignment="Center"/>
                        </StackPanel>

                        <!-- أزرار الهوامش المحددة مسبقاً -->
                        <StackPanel Grid.Row="1" Grid.ColumnSpan="4" Orientation="Horizontal"
                                    HorizontalAlignment="Center" Margin="0,10,0,0">
                            <Button Content="هامش (1مم)" Click="SetMargin1_Click"
                                    Margin="5,0" Padding="10,5" Background="#E3F2FD" Foreground="#1976D2"/>
                            <Button Content="هامش (2مم)" Click="SetMargin2_Click"
                                    Margin="5,0" Padding="10,5" Background="#E8F5E8" Foreground="#388E3C"/>
                            <Button Content="ضيق (12مم)" Click="SetNarrowMargins_Click"
                                    Margin="5,0" Padding="10,5"/>
                            <Button Content="عادي (25مم)" Click="SetNormalMargins_Click"
                                    Margin="5,0" Padding="10,5"/>
                            <Button Content="واسع (50مم)" Click="SetWideMargins_Click"
                                    Margin="5,0" Padding="10,5"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <Button Name="PreviewButton" Content="🖼️ معاينة" Click="PreviewButton_Click" 
                    Background="#2196F3" Foreground="White" Padding="15,8" Margin="5,0" 
                    FontWeight="Bold" MinWidth="100"/>
            <Button Name="PrintButton" Content="🖨️ طباعة" Click="PrintButton_Click" 
                    Background="#4CAF50" Foreground="White" Padding="15,8" Margin="5,0" 
                    FontWeight="Bold" MinWidth="100"/>
            <Button Name="SaveSettingsButton" Content="💾 حفظ الإعدادات" Click="SaveSettingsButton_Click" 
                    Background="#FF9800" Foreground="White" Padding="15,8" Margin="5,0" 
                    FontWeight="Bold" MinWidth="120"/>
            <Button Name="CancelButton" Content="❌ إلغاء" Click="CancelButton_Click" 
                    Background="#F44336" Foreground="White" Padding="15,8" Margin="5,0" 
                    FontWeight="Bold" MinWidth="100"/>
        </StackPanel>
    </Grid>
</Window>
