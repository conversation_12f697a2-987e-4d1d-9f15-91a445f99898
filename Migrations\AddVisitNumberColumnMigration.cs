using System;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

namespace SFDSystem.Migrations
{
    /// <summary>
    /// Migration لإضافة عمود VisitNumber إلى جدول DriverQuotes
    /// </summary>
    public static class AddVisitNumberColumnMigration
    {
        /// <summary>
        /// تطبيق Migration لإضافة العمود الجديد
        /// </summary>
        public static async Task<bool> ApplyAsync(string connectionString)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة عمود VisitNumber إلى جدول DriverQuotes...");

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // التحقق من وجود العمود أولاً
                var checkColumnSql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'VisitNumber'
                ";

                using var checkCommand = new SqlCommand(checkColumnSql, connection);
                var columnExists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                if (!columnExists)
                {
                    // إضافة العمود الجديد
                    var addColumnSql = @"
                        ALTER TABLE DriverQuotes 
                        ADD VisitNumber NVARCHAR(50) NULL
                    ";

                    using var addCommand = new SqlCommand(addColumnSql, connection);
                    await addCommand.ExecuteNonQueryAsync();

                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود VisitNumber إلى جدول DriverQuotes بنجاح");

                    // إنشاء فهرس للعمود الجديد
                    try
                    {
                        var indexSql = @"
                            CREATE INDEX IX_DriverQuote_VisitNumber 
                            ON DriverQuotes (VisitNumber)
                        ";

                        using var indexCommand = new SqlCommand(indexSql, connection);
                        await indexCommand.ExecuteNonQueryAsync();
                        System.Diagnostics.Debug.WriteLine("✅ تم إنشاء فهرس IX_DriverQuote_VisitNumber بنجاح");
                    }
                    catch (Exception indexEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ تحذير: فشل في إنشاء الفهرس: {indexEx.Message}");
                        // لا نرمي خطأ إذا فشل إنشاء الفهرس
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ عمود VisitNumber موجود مسبقاً في جدول DriverQuotes");
                }

                await connection.CloseAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق migration إضافة عمود VisitNumber بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق migration إضافة عمود VisitNumber: {ex.Message}");
                
                // لا نرمي الخطأ إذا كان العمود موجوداً مسبقاً
                if (ex.Message.Contains("already exists") || 
                    ex.Message.Contains("Invalid column name") ||
                    ex.Message.Contains("duplicate column"))
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ العمود موجود مسبقاً - تجاهل الخطأ");
                    return true;
                }
                
                return false;
            }
        }

        /// <summary>
        /// التراجع عن Migration (إزالة العمود)
        /// </summary>
        public static async Task<bool> RollbackAsync(string connectionString)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إزالة عمود VisitNumber من جدول DriverQuotes...");

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // التحقق من وجود العمود أولاً
                var checkColumnSql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'VisitNumber'
                ";

                using var checkCommand = new SqlCommand(checkColumnSql, connection);
                var columnExists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                if (columnExists)
                {
                    // إزالة الفهرس أولاً
                    try
                    {
                        var dropIndexSql = "DROP INDEX IF EXISTS IX_DriverQuote_VisitNumber ON DriverQuotes";
                        using var dropIndexCommand = new SqlCommand(dropIndexSql, connection);
                        await dropIndexCommand.ExecuteNonQueryAsync();
                        System.Diagnostics.Debug.WriteLine("✅ تم حذف فهرس IX_DriverQuote_VisitNumber");
                    }
                    catch (Exception indexEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ تحذير: فشل في حذف الفهرس: {indexEx.Message}");
                    }

                    // إزالة العمود
                    var dropColumnSql = "ALTER TABLE DriverQuotes DROP COLUMN VisitNumber";
                    using var dropCommand = new SqlCommand(dropColumnSql, connection);
                    await dropCommand.ExecuteNonQueryAsync();

                    System.Diagnostics.Debug.WriteLine("✅ تم حذف عمود VisitNumber من جدول DriverQuotes بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ عمود VisitNumber غير موجود في جدول DriverQuotes");
                }

                await connection.CloseAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق rollback migration بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق rollback migration: {ex.Message}");
                return false;
            }
        }
    }
}
