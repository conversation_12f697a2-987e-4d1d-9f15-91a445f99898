using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using System;
using System.Windows;

namespace DriverManagementSystem.Views
{
    public partial class DashboardView : UserControl
    {
        public DashboardView()
        {
            InitializeComponent();
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 إنشاء DashboardView");
                var viewModel = new DashboardViewModel();
                DataContext = viewModel;
                System.Diagnostics.Debug.WriteLine($"✅ تم ربط ViewModel - عدد الأزرار: {viewModel.QuickActions.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء DashboardView: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }


    }
}
