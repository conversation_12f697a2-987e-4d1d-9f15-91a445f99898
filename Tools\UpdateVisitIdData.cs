using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Tools
{
    /// <summary>
    /// أداة لتحديث بيانات VisitId في قاعدة البيانات
    /// </summary>
    public static class UpdateVisitIdData
    {
        /// <summary>
        /// تحديث بيانات VisitId
        /// </summary>
        public static async Task UpdateDataAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                Console.WriteLine("🔄 بدء تحديث بيانات VisitId...");

                // تحديث VisitId في جدول FieldVisits ليكون مساوياً لـ Id
                var fieldVisitsUpdated = await context.Database.ExecuteSqlRawAsync(@"
                    UPDATE [FieldVisits] 
                    SET [VisitId] = [Id] 
                    WHERE [VisitId] = 0 OR [VisitId] IS NULL;
                ");
                Console.WriteLine($"✅ تم تحديث {fieldVisitsUpdated} زيارة في جدول FieldVisits");

                // تحديث VisitId في جدول DriverQuotes بناءً على VisitNumber
                var driverQuotesUpdated = await context.Database.ExecuteSqlRawAsync(@"
                    UPDATE dq 
                    SET dq.[VisitId] = fv.[VisitId]
                    FROM [DriverQuotes] dq
                    INNER JOIN [FieldVisits] fv ON dq.[VisitNumber] = fv.[VisitNumber]
                    WHERE dq.[VisitId] = 0 OR dq.[VisitId] IS NULL;
                ");
                Console.WriteLine($"✅ تم تحديث {driverQuotesUpdated} عرض في جدول DriverQuotes");

                // التحقق من النتائج
                var fieldVisitsCount = await context.FieldVisits.CountAsync();
                var fieldVisitsWithVisitId = await context.FieldVisits.CountAsync(fv => fv.VisitId > 0);
                
                var driverQuotesCount = await context.DriverQuotes.CountAsync();
                var driverQuotesWithVisitId = await context.DriverQuotes.CountAsync(dq => dq.VisitId > 0);

                Console.WriteLine("\n📊 النتائج:");
                Console.WriteLine($"   - FieldVisits: {fieldVisitsWithVisitId}/{fieldVisitsCount} لها VisitId");
                Console.WriteLine($"   - DriverQuotes: {driverQuotesWithVisitId}/{driverQuotesCount} لها VisitId");

                Console.WriteLine("\n✅ تم تحديث البيانات بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحديث البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// نقطة الدخول الرئيسية
        /// </summary>
        public static async Task Main(string[] args)
        {
            try
            {
                await UpdateDataAsync();
                Console.WriteLine("\nاضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }
    }
}
